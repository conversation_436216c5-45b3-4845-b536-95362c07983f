// Color palette for WakeMeGo app
export const COLORS = {
  // Primary gradient colors
  gradientStart: '#FF7EB3', // Light pink
  gradientEnd: '#FF758C',   // Deeper pink
  
  // Background colors
  backgroundWhite: '#FFFFFF',
  backgroundGray: '#F9F9F9',
  
  // Text colors
  textPrimary: '#333333',
  textSecondary: '#666666',
  textLight: '#FFFFFF',
  
  // Status colors
  success: '#4CAF50',
  warning: '#FF9800',
  error: '#F44336',
  
  // Map colors
  geofenceStroke: '#FF758C',
  geofenceFill: 'rgba(255, 126, 179, 0.2)',
  currentLocationMarker: '#4CAF50',
  
  // UI elements
  cardShadow: 'rgba(0, 0, 0, 0.1)',
  borderLight: '#E0E0E0',
  
  // Transparent overlays
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(255, 255, 255, 0.9)',
};
